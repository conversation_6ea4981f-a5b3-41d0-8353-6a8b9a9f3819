{"name": "demo-sdk-js", "version": "0.1.0", "private": true, "homepage": "https://okx.github.io/wallet-sdk-demo", "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@okxweb3/coin-base": "^1.0.9", "@okxweb3/coin-bitcoin": "^1.0.23", "@okxweb3/coin-ethereum": "^1.0.3", "@okxweb3/coin-ton": "^1.0.0", "@okxweb3/crypto-lib": "^1.0.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^28.1.8", "@types/node": "^12.20.55", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "framer-motion": "^6.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^3.11.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"predeploy": "npm run build", "deploy": "gh-pages -d build", "start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"assert": "^2.1.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "gh-pages": "^6.1.1", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "react-router-dom": "^6.26.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.3", "vm-browserify": "^1.1.2"}}